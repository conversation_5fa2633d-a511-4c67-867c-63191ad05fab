{"info": {"_postman_id": "10bd83d2-1367-4512-ad8e-323d5ffc2abc", "name": "#5398 АПИ. Настройки опроса. Определение тональности и тегов для открытого ответа с помощью ИИ", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "17930641"}, "item": [{"name": "Сохранение настроек", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "default_moderator_id", "value": "", "type": "text"}, {"key": "default_executor_id", "value": "", "type": "text"}, {"key": "processing_time_in_minutes", "value": "0", "type": "text"}, {"key": "processing_time_by_link_in_minutes", "value": "0", "type": "text"}, {"key": "processing_time_for_client_in_minutes", "value": "0", "type": "text"}, {"key": "notificationScript", "value": "", "type": "text"}, {"key": "need_auth", "value": "0", "type": "text"}, {"key": "ai_enabled", "value": "1", "description": "Определение тональности и тегов для открытого ответа с помощью искусственного интеллекта", "type": "text"}]}, "url": {"raw": "{{base_url}}/foquz/api/poll/save-settings?id=42921&access-token=30ZKhybu_U1MLi0", "host": ["{{base_url}}"], "path": ["foquz", "api", "poll", "save-settings"], "query": [{"key": "id", "value": "42921"}, {"key": "access-token", "value": "30ZKhybu_U1MLi0"}]}}, "response": []}, {"name": "Настройки компании", "request": {"method": "GET", "header": [], "url": "http://localhost:8081/foquz/settings/request-processing-settings"}, "response": []}, {"name": "Отправка заявки на подключение", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "phone", "value": "5423534535", "type": "text", "disabled": true}]}, "url": {"raw": "http://localhost:8081/foquz/api/poll/send-ai-processing-request?access-token=30ZKhybu_U1MLi0", "protocol": "http", "host": ["localhost"], "port": "8081", "path": ["foquz", "api", "poll", "send-ai-processing-request"], "query": [{"key": "access-token", "value": "30ZKhybu_U1MLi0"}]}}, "response": []}, {"name": "Сайд<PERSON>ит, добавление тега", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "tags[0][id]", "value": "блабла1", "description": "название тега", "type": "text"}, {"key": "tags[0][name]", "value": "блабла1", "description": "название тега (в анкетах это дублируется, сделал также)", "type": "text"}, {"key": "tags[1][id]", "value": "блабла2", "type": "text"}, {"key": "tags[1][name]", "value": "блабла2", "type": "text"}, {"key": "question_ai_id", "value": "1", "description": "id из сохранения настроек сайдшита", "type": "text"}]}, "url": {"raw": "{{base_url}}/foquz/api/questions/add-ai-tag?id=136329&access-token=30ZKhybu_U1MLi0", "host": ["{{base_url}}"], "path": ["foquz", "api", "questions", "add-ai-tag"], "query": [{"key": "id", "value": "136329"}, {"key": "access-token", "value": "30ZKhybu_U1MLi0"}]}}, "response": []}, {"name": "Сайд<PERSON>ит, удаление тега", "request": {"method": "DELETE", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "tags[]", "value": "970", "description": "id тега", "type": "text"}, {"key": "question_ai_id", "value": "1", "description": "id из сохранения настроек сайдшита", "type": "text"}, {"key": "tags[]", "value": "971", "type": "text"}]}, "url": {"raw": "{{base_url}}/foquz/api/questions/remove-ai-tag?id=136329&access-token=30ZKhybu_U1MLi0", "host": ["{{base_url}}"], "path": ["foquz", "api", "questions", "remove-ai-tag"], "query": [{"key": "id", "value": "136329"}, {"key": "access-token", "value": "30ZKhybu_U1MLi0"}]}}, "response": []}, {"name": "Сохранение настроек", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "FoquzQuestion[poll_id]", "value": "42931", "type": "text"}, {"key": "FoquzQuestion[description]", "value": "<p style=\"text-align:center;\">ЗВ</p>", "type": "text"}, {"key": "FoquzQuestion[main_question_type]", "value": "15", "type": "text"}, {"key": "FoquzQuestion[is_required]", "value": "1", "type": "text"}, {"key": "FoquzQuestion[enableGallery]", "value": "1", "type": "text"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>[skip]", "value": "1", "type": "text"}, {"key": "FoquzQuestion[skip_text]", "value": "", "type": "text"}, {"key": "FoquzQuestion[comment_enabled]", "value": "1", "type": "text"}, {"key": "FoquzQuestion[placeholder_text]", "value": "Подсказка для коммента", "type": "text"}, {"key": "FoquzQuestion[comment_minlength]", "value": "0", "type": "text"}, {"key": "FoquzQuestion[comment_maxlength]", "value": "100", "type": "text"}, {"key": "FoquzQuestion[comment_label]\n", "value": "Коммент", "type": "text"}, {"key": "FoquzQuestion[comment_required]", "value": "0", "type": "text"}, {"key": "FoquzQuestion[service_name]", "value": "", "type": "text"}, {"key": "FoquzQuestion[starOptions][color]", "value": "rgb(248, 205, 28)", "type": "text"}, {"key": "FoquzQuestion[starOptions][count]", "value": "4", "type": "text"}, {"key": "FoquzQuestion[starOptions][labels][0]", "value": "", "type": "text"}, {"key": "FoquzQuestion[starOptions][labels][1]", "value": "", "type": "text"}, {"key": "FoquzQuestion[starOptions][labels][2]", "value": "", "type": "text"}, {"key": "FoquzQuestion[starOptions][labels][3]", "value": "", "type": "text"}, {"key": "FoquzQuestion[starOptions][labels][4]", "value": "", "type": "text"}, {"key": "FoquzQuestion[starOptions][labels][5]", "value": "", "type": "text"}, {"key": "FoquzQuestion[aiSettings][option_type]", "value": "", "description": "1-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 2-свой вариант, 3-текстовый ответ", "type": "text"}, {"key": "FoquzQuestion[aiSettings][ai_tonality_enabled]", "value": "", "description": "Опция Определение тональности с помощью ИИ", "type": "text"}, {"key": "FoquzQuestion[aiSettings][ai_tag_enabled]", "value": "", "description": "Опция Тегирование с помощью ИИ", "type": "text"}, {"key": "FoquzQuestion[aiSettings][tonality_prompt]", "value": "", "description": "Промт для определения тональности", "type": "text"}, {"key": "FoquzQuestion[aiSettings][tag_prompt]", "value": "", "description": "Промт для тегирования", "type": "text"}]}, "url": {"raw": "http://localhost:8081/foquz/api/questions/update?id=136331&access-token=30ZKhybu_U1MLi0", "protocol": "http", "host": ["localhost"], "port": "8081", "path": ["foquz", "api", "questions", "update"], "query": [{"key": "id", "value": "136331"}, {"key": "access-token", "value": "30ZKhybu_U1MLi0"}]}}, "response": []}]}