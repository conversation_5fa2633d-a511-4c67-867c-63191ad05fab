<?php

declare(strict_types=1);


namespace app\modules\foquz\services;

use app\models\User;
use app\modules\foquz\models\FoquzCompanyTag;
use app\modules\foquz\models\FoquzContactTag;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzQuestionAi;
use app\modules\foquz\models\FoquzQuestionAiTag;
use Yii;
use yii\db\Exception;
use yii\web\IdentityInterface;

class AnswerTagService
{
    /**
     * Добавление тегов в анкету или в настройки тегирования с помощью ИИ
     * @param array $answers массив Ids анкет
     * @param array $tags массив тегов
     * @param User $user
     * @param int|null $questionAiId Id настроек ИИ тегирования
     * @return void
     * @throws Exception
     * @throws ValidateException
     * @throws \Throwable
     */
    public function addTags(array $answers, array $tags, User $user, ?int $questionAiId = null): void
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            foreach ($tags as $t) {
                if (empty($t['name'])) {
                    throw ValidateException::make('tags', 'Не задано имя тега');
                }

                // Создаем или находим тег
                $tag = $this->findOrCreateTag($t['name'], $user->company->id);

                if ($tag->auto_add) {
                    throw ValidateException::make('tags', 'Невозможно добавить тег с автоматическим добавлением');
                }

                // Обработка ответов
                if (!empty($answers)) {
                    $this->processAnswers($answers, $tag->id, $user);
                }

                // Обработка настроек ИИ
                if ($questionAiId) {
                    $this->processAiTag($questionAiId, $tag->id, $user);
                }
            }

            $transaction->commit();

        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    private function findOrCreateTag(string $tagName, int $companyId): FoquzCompanyTag
    {
        $tag = FoquzCompanyTag::findOne([
            'tag' => $tagName,
            'company_id' => $companyId
        ]);

        if (!$tag) {
            $tag = new FoquzCompanyTag([
                'tag' => $tagName,
                'company_id' => $companyId
            ]);

            if (!$tag->save()) {
                $errors = implode(', ', $tag->getFirstErrors());
                throw new Exception("Ошибка сохранения тега: " . $errors);
            }
        }

        return $tag;
    }

    private function processAnswers(array $answerIds, int $tagId, User $user): void
    {
        if (!empty($answerIds)) {
            FoquzContactTag::deleteAll(['tag_id' => $tagId, 'answer_id' => $answerIds]);
        }

        foreach ($answerIds as $answerId) {
            $answer = FoquzPollAnswer::findOne($answerId);
            if (!$answer) {
                throw ValidateException::make('answer', 'Не найден ответ с ID ' . $answerId);
            }

            $this->checkAccess($answer, $user);

            if (FoquzContactTag::find()->where(['answer_id' => $answerId, 'tag_id' => $tagId])->exists()) {
                throw ValidateException::make('tags', 'Тег уже выбран');
            }

            $contactTag = new FoquzContactTag([
                'created_by' => Yii::$app->user->id,
                'tag_id' => $tagId,
                'answer_id' => $answerId,
                'contact_id' => null
            ]);

            $contactTag->setScenario(FoquzContactTag::SCENARIO_TAG_ANSWER);

            if (!$contactTag->save()) {
                $errors = implode(', ', $contactTag->getFirstErrors());
                throw new Exception('Не удалось записать тег: ' . $errors);
            }
        }
    }

    private function processAiTag(int $questionAiId, int $tagId, User $user): void
    {
        // Проверяем доступ к настройкам ИИ
        $questionAi = FoquzQuestionAi::findOne($questionAiId);
        if (!$questionAi) {
            throw ValidateException::make('question_ai', 'Настройки ИИ не найдены');
        }

        if ($questionAi->question->poll->company_id !== $user->company->id) {
            throw ValidateException::make('question_ai', 'Доступ запрещен', 403);
        }

        if (FoquzQuestionAiTag::find()->where([
            'tag_id' => $tagId,
            'question_ai_id' => $questionAiId
        ])->exists()) {
            throw ValidateException::make('tags', 'Тег уже выбран');
        }

        $aiTag = new FoquzQuestionAiTag([
            'created_by' => Yii::$app->user->id,
            'updated_by' => Yii::$app->user->id,
            'tag_id' => $tagId,
            'question_ai_id' => $questionAiId
        ]);

        if (!$aiTag->save()) {
            $errors = implode(', ', $aiTag->getFirstErrors());
            throw new Exception('Не удалось записать тег ИИ: ' . $errors);
        }
    }


    /**
     * Удаление тегов из анкеты
     * @param array $answers
     * @param array $tags
     * @param User $user
     * @return void
     * @throws ValidateException
     * @throws \Throwable
     */
    public function removeTags(array $answers, array $tags, User $user): void
    {
        foreach ($answers as $answerId) {
            $answer = FoquzPollAnswer::findOne($answerId);

            if (!$answer) {
                throw ValidateException::make('answer', Yii::t('main', 'Не найден ответ с ID ' . $answerId));
            }

            $error = $this->checkAccess($answer, $user);
            if ($error instanceof ValidateException) {
                throw $error;
            }

            $answer->removeTags($tags);
        }
    }

    private function checkAccess(FoquzPollAnswer $answer, User $user): ?ValidateException
    {
        if (!$user->company->isContactsAccessEnabled()) {
            return ValidateException::make('answer', Yii::t('main', 'Для добавления тега к анкете необходимо подключить раздел «Контакты»'), 403);
        }

        if ($answer->foquzPoll->company_id !== $user->company->id) {
            return ValidateException::make('answer', Yii::t('main', 'Ответ не принадлежит компании пользователя'), 403);
        }

        return null;
    }
}