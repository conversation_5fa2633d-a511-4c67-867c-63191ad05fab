<?php

namespace app\modules\foquz\models;

use app\modules\foquz\exceptions\ValidationException;
use DateTime;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\web\BadRequestHttpException;

/**
 * This is the model class for table "foquz_question_ai".
 *
 * @property int $id
 * @property int $question_id
 * @property int $option_type Тип опции, к которой подключены настройки (Комментарий, Свой вариант, Текстовый ответ
 * @property string|null $tonality_prompt Промт для определения тональности
 * @property string|null $tag_prompt Промт для тегирования
 * @property int $ai_tonality_enabled Галочка Определение тональности с помощью ИИ
 * @property int $ai_tag_enabled Галочка Тегирование с помощью ИИ
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property FoquzQuestion $question
 */
class FoquzQuestionAi extends \yii\db\ActiveRecord
{
    public const OPTION_TYPE_COMMENT = 1;
    public const OPTION_TYPE_SELF_VARIANT = 2;
    public const OPTION_TYPE_TEXT = 3;


    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'foquz_question_ai';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => (new DateTime('now'))->format('Y-m-d H:i:s')
            ]
        ];
    }

    public function fields(): array
    {
        return [
            'id',
            'question_id',
            'option_type' => fn(FoquzQuestionAi $model) => (int)$model->option_type,
            'tonality_prompt',
            'tag_prompt',
            'ai_tonality_enabled',
            'ai_tag_enabled',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['question_id', 'option_type'], 'required'],
            [['question_id', 'option_type', 'ai_tonality_enabled', 'ai_tag_enabled'], 'integer'],
            ['option_type', 'in', 'range' => [
                self::OPTION_TYPE_COMMENT,
                self::OPTION_TYPE_SELF_VARIANT,
                self::OPTION_TYPE_TEXT
            ], 'message' => 'Invalid option type'],
            [['tonality_prompt', 'tag_prompt'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['question_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzQuestion::class, 'targetAttribute' => ['question_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'question_id' => 'Question ID',
            'option_type' => 'Тип опции, к которой подключены настройки (Комментарий, Свой вариант, Текстовый ответ',
            'tonality_prompt' => 'Промт для определения тональности',
            'tag_prompt' => 'Промт для тегирования',
            'ai_tonality_enabled' => 'Галочка Определение тональности с помощью ИИ',
            'ai_tag_enabled' => 'Галочка Тегирование с помощью ИИ',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Question]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getQuestion()
    {
        return $this->hasOne(FoquzQuestion::class, ['id' => 'question_id']);
    }

    public static function upsertRecord(int $questionId, int $optionType)
    {

        if (!in_array($optionType, [self::OPTION_TYPE_COMMENT, self::OPTION_TYPE_TEXT, self::OPTION_TYPE_SELF_VARIANT])) {
            throw ValidationException::make(['option_type' => 'Неправильное значение параметра']);
        }

        $aiSettings = self::findOne([
            'question_id' => $questionId,
            'option_type' => $optionType
        ]);
        if ($aiSettings) {
            return $aiSettings;
        }

        $aiSettings = new self();
        $aiSettings->question_id = $questionId;
        $aiSettings->option_type = $optionType;
        $aiSettings->save(false);
        return $aiSettings;
    }

    public static function upsertRecords(int $questionId, array $post_data): void
    {

        foreach ($post_data as $item) {

            if (empty($item['option_type'])) {

                throw ValidationException::make(['option_type' => 'Не передан обязательный параметр']);
            }

            $aiSettings = self::findOne([
                'question_id' => $questionId,
                'option_type' => $item['option_type']
            ]) ?? new self();

            if (!$aiSettings->load($item, '')) {
                throw new BadRequestHttpException('Ошибка загрузки данных');
            }

            $aiSettings->question_id = $questionId;
            $aiSettings->ai_tonality_enabled = (int)($item['ai_tonality_enabled'] ?? 0);
            $aiSettings->ai_tag_enabled = (int)($item['ai_tag_enabled'] ?? 0);
            $aiSettings->option_type = (int)$item['option_type'];

            if ($aiSettings->ai_tonality_enabled && empty($aiSettings->tonality_prompt)) {
                throw ValidationException::make(['tonality_prompt' => 'Не передан обязательный параметр']);
            }

            if ($aiSettings->ai_tag_enabled && empty($aiSettings->tag_prompt)) {
                throw ValidationException::make(['tag_prompt' => 'Не передан обязательный параметр']);
            }

            if (!$aiSettings->validate() || !$aiSettings->save()) {
                throw ValidationException::make($aiSettings->getFirstErrors());
            }
        }

    }
}
