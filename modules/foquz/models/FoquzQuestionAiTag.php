<?php

namespace app\modules\foquz\models;

use app\models\User;
use app\modules\foquz\exceptions\ValidationException;
use app\modules\foquz\services\AnswerTagService;
use DateTime;
use Yii;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "foquz_question_ai_tag".
 *
 * @property int $id
 * @property string $created_at
 * @property string $updated_at
 * @property int $created_by
 * @property int $updated_by
 * @property int $tag_id
 * @property int $question_ai_id
 *
 * @property FoquzQuestionAi $questionAi
 * @property FoquzCompanyTag $tag
 */
class FoquzQuestionAiTag extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'foquz_question_ai_tag';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => (new DateTime('now'))->format('Y-m-d H:i:s')
            ],
            BlameableBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['created_by', 'updated_by', 'tag_id', 'question_ai_id'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['created_by', 'updated_by', 'tag_id', 'question_ai_id'], 'integer'],
            [['question_ai_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzQuestionAi::class, 'targetAttribute' => ['question_ai_id' => 'id']],
            [['tag_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzCompanyTag::class, 'targetAttribute' => ['tag_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'tag_id' => 'Tag ID',
            'question_ai_id' => 'Question Ai ID',
        ];
    }

    /**
     * Gets query for [[QuestionAi]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getQuestionAi()
    {
        return $this->hasOne(FoquzQuestionAi::class, ['id' => 'question_ai_id']);
    }

    /**
     * Gets query for [[Tag]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTag()
    {
        return $this->hasOne(FoquzCompanyTag::class, ['id' => 'tag_id']);
    }

    public static function addTag(int $question_ai, array $tags, User $user): void
    {
        $answerTagService = new AnswerTagService();
        /** @var User $user */
        $answerTagService->addTags(
            [],
            $tags,
            $user,
            $question_ai
        );
    }

    public static function removeTag(int $questionAiId, array $tags, int $questionId): void
    {
        $deleteIds = [];
        foreach ($tags as $tag) {

            $aiTag = self::find()
                ->alias('ai_tag')
                ->innerJoinWith(['questionAi' => function($query) use ($questionId) {
                    $query->andWhere(['foquz_question_ai.question_id' => $questionId]);
                }])
                ->where([
                    'ai_tag.question_ai_id' => $questionAiId,
                    'ai_tag.tag_id' => $tag
                ])
                ->one();

            if (!$aiTag) {
                throw ValidationException::make(['answer' => 'Неправильный идентификатор записи']);
            }
            $deleteIds[] = $aiTag->id;
        }

        if (!empty($deleteIds)) {
            self::deleteAll(['id' => $deleteIds]);
        }
    }
}
