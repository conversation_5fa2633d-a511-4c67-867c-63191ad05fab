<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_question_ai}}`.
 */
class m250908_090834_create_foquz_question_ai_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_question_ai}}', [
            'id' => $this->primaryKey(),
            'question_id' => $this->integer()->notNull(),
            'option_type' => $this->integer()->notNull()
                ->comment('Тип опции, к которой подключены настройки (Комментарий, Свой вариант, Текстовый ответ'),
            'tonality_prompt' => $this->text()->null()
                ->comment('Промт для определения тональности'),
            'tag_prompt' => $this->text()->null()
                ->comment('Промт для тегирования'),
            'ai_tonality_enabled' => $this->boolean()->notNull()->defaultValue(0)
                ->comment('Галочка Определение тональности с помощью ИИ'),
            'ai_tag_enabled' => $this->boolean()->notNull()->defaultValue(0)
                ->comment('Галочка Тегирование с помощью ИИ'),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ]);

        $this->addForeignKey(
            'fk-foquz_question_ai_question_id',
            '{{%foquz_question_ai}}',
            'question_id',
            '{{%foquz_question}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-foquz_question_ai_question_id', '{{%foquz_question_ai}}');
        $this->dropTable('{{%foquz_question_ai}}');
    }
}
