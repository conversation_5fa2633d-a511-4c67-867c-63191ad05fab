<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_question_ai_tag}}`.
 */
class m250908_101033_create_foquz_question_ai_tag_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_question_ai_tag}}', [
            'id' => $this->primaryKey(),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
            'created_by' => $this->integer()->notNull(),
            'updated_by' => $this->integer()->notNull(),
            'tag_id' => $this->integer()->notNull(),
            'question_ai_id' => $this->integer()->notNull(),
        ]);

        $this->addForeignKey(
            'fk-foquz_question_ai_tag_tag_id',
            '{{%foquz_question_ai_tag}}',
            'tag_id',
            '{{%foquz_company_tag}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-foquz_question_ai_tag_question_ai_id',
            '{{%foquz_question_ai_tag}}',
            'question_ai_id',
            '{{%foquz_question_ai}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%foquz_question_ai_tag}}');
    }
}
