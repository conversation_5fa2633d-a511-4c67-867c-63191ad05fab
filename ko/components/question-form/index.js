import { get as _get } from "lodash";

import * as models from "./models";
import * as types from "Data/question-types";

import "./components/question-form-variants-list";
import "./components/question-form-variant";
import "./components/question-form-media-variants-list";
import "./components/question-form-media-variant";
import "./components/matrix-controller";
import "./dialogs/donor-type-dialog";

import { getCompanyFilials } from "@/api/company/get-filials";
import { getCompanyCollections } from "@/api/collections/get-collections";
import { getCollectionRequest } from "@/api/collections/collection";

import { questionTypeOptionTemplate } from "Legacy/utils/select2/templates/question-type";

import ee from "event-emitter";

import { Translator } from "@/utils/translate";
import { DialogsModule } from "@/utils/dialogs-module";

const QuestionTranslator = Translator("question");

import "./style.less";

import "./components/type-select";
import "./components/ai-processing-field-sidesheet-trigger";

const QuestionModels = {
  [types.RATE_QUESTION]: models.RateQuestion,
  [types.VARIANTS_QUESTION]: models.VariantsQuestion,
  [types.TEXT_QUESTION]: models.TextQuestion,
  [types.DATE_QUESTION]: models.DateQuestion,
  [types.ADDRESS_QUESTION]: models.AddressQuestion,
  [types.FILE_QUESTION]: models.FileQuestion,
  [types.QUIZ_QUESTION]: models.QuizQuestion,
  [types.PRIORITY_QUESTION]: models.PriorityQuestion,
  [types.MEDIA_VARIANTS_QUESTION]: models.MediaVariantsQuestion,
  [types.GALLERY_QUESTION]: models.GalleryQuestion,
  [types.SMILE_QUESTION]: models.SmileQuestion,
  [types.NPS_QUESTION]: models.NPSQuestion,
  [types.MATRIX_QUESTION]: models.MatrixQuestion,
  [types.DIFF_QUESTION]: models.DiffQuestion,
  [types.STARS_QUESTION]: models.StarsQuestion,
  [types.STAR_VARIANTS_QUESTION]: models.StarVariantsQuestion,
  [types.RATING_QUESTION]: models.RatingQuestion,
  [types.CLASSIFIER_QUESTION]: models.ClassifierQuestion,
  [types.FILIALS_QUESTION]: models.FilialQuestion,
  [types.INTER_BLOCK]: models.InterBlock,
  [types.SCALE_QUESTION]: models.ScaleQuestion,
  [types.DISTRIBUTION_SCALE_QUESTION]: models.DistributionScaleQuestion,
  [types.MATRIX_3D_QUESTION]: models.Matrix3DQuestion,
  [types.CARD_SORTING_QUESTION]: models.CardSortingQuestion,
  [types.FIRST_CLICK_TEST]: models.FirstClickTestQuestion,
};

class QuestionFormController {
  constructor(config) {
    ee(this);
    DialogsModule(this);
    this.translator = QuestionTranslator;
    this.poll = config.poll;
    this.pollAiEnabled = ko.observable(!!_get(this.poll, 'ai_enabled'));
    this.companyAiEnabled = ko.observable(!!window.IS_AI_ENABLED);
    this.isBlocked = config.blocked;
    this.updating = ko.observable(false);

    this.mode = config.mode;

    this.formControlErrorStateMatcher = config.formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher = config.formControlSuccessStateMatcher;

    this.api = config.api;

    this.isAuto = config.isAuto;
    this.hasOrder = !!config.hasOrder;
    this.withPoints = !!config.withPoints;
    this.tryChangeBlockedParam = (question) => {
      config.tryChangeBlockedParam(question);
    };
    this.noDonorsInfo = config.noDonorsInfo;
    this.activateQuestion = config.activateQuestion;

    this.types = types;

    this.question = ko.observable(null);

    this.isChanged = ko.observable(false).extend({ notify: "always" });

    this.donors = config.donors;
    this.recipients = config.recipients;
    this.blockRecipients = config.blockRecipients;

    this.filials = ko.observableArray([]);
    this.filialsLoaded = ko.observable(false);
    this.filialsLoadedPromise = ko.observable(this.loadFilials().then((filials) => {
      this.filials(filials);
      this.filialsLoaded(true);
    }));


    this.collections = ko.observableArray([]);
    this.collectionsLoaded = ko.observable(false);
    this.loadCollections().then((collections) => {
      this.collections(collections);
      this.collectionsLoaded(true);
    });

    this.dictionary = ko.observable({});
    this.dictionaryLoaded = ko.observable(false);
    this.dictionaryElements = ko.observable([]);
    this.dictionaryConnectionSwitchDisabled = ko.computed(() => {
      return parseInt(_get(this.poll, 'dictionary_count', 0)) === 0;
    }, this);

    this.question.subscribe((v) => {
      if (this.blocked) {
        v.block();
      }
      v.dictionaryElementId.subscribe(dictionaryElementId => {
        if (dictionaryElementId && !this.dictionaryLoaded()) {
          this.dictionary({
            count: POLL.dictionary_count,
            description: "",
            elements: [
              {
                id: dictionaryElementId,
                title: v.dictionaryElementName,
              },
            ],
            id: POLL.dictionary_id,
            is_active: 1,
            name: POLL.dictionary_name,
            system: 0,
            used: 0,
          })
        }
      });

      this.onChange(1);
      v.on("changeScreenType", (data) => {
        this.emit(QuestionFormController.events.changeScreenType, data);
      });
      v.on("changeShowNumber", (data) => {
        this.emit(QuestionFormController.events.changeShowNumber, data);
      });
    });

    this.disableStartScreen = ko.observable(false);
  }

  loadFilials() {
    return getCompanyFilials();
  }

  loadCollections() {
    return getCompanyCollections().then((list) => {
      return list.filter((item) => item.active && !item.system);
    });
  }

  loadDictionary() {
    return getCollectionRequest(POLL.dictionary_id, POLL.company_id).then(data => {
      this.dictionary(data.data.dictionary);
      const dictionaryElements = [];
      const prepareItem = (item) => {
        if (item.type === 'element') {
          dictionaryElements.push(item);
          return;
        }
        item.elements.forEach(dEl => prepareItem(dEl));
      };
      data.data.dictionary.elements.forEach(dEl => prepareItem(dEl));
      this.dictionaryElements(dictionaryElements);
      this.dictionaryLoaded(true);
    });
  }

  openDonorTypeModal(isDictionary) {
    this.openDialog({
      name: "donor-type-dialog",
      params: {
        text: isDictionary ? 'Справочник классификатора можно изменить только после удаления всех связей вопроса с вопросами-реципиентами:' : undefined,
        recipients: this.recipients(),
        toRecipient: (recipient) => {
          this.activateQuestion(recipient.question.id);
        },
      },
    });
  }

  getQuestionModel({ type, alias }) {
    // if (this.question() && String(this.question()?.type) === String(type)) {
    //   return this.question();
    // }
    if (type == types.RATE_QUESTION && alias === "Товар") {
      return new models.ItemsQuestion(this);
    }
    return new QuestionModels[type](this);
  }

  getQuestionScreenType(q) {
    if (!q) return "";
    if (q.isInterblock) return q.blockType();
    return "default";
  }

  createQuestionModel(questionData) {
    let question = this.getQuestionModel(questionData);
    question.updateData(questionData);
    return question;
  }

  wrapQuestion(questionData) {
    let question = this.getQuestionModel(questionData);
    question.updateData(questionData);
    return question.getData();
  }

  setQuestion(questionData) {
    this.updating(true);
    let currentQuestion = this.question();

    let question = null;
    const isNewQuestion = !questionData
      || ((questionData.type == types.STARS_QUESTION) && !questionData.description)
    console.log('debug: setQuestion: isNewQuestion', isNewQuestion);
    if (isNewQuestion) {
      question = new models.StarsQuestion(this);
    } else {
      question = this.getQuestionModel(questionData);
    }

    console.log('hha');

    if (currentQuestion) {

      console.log('debug: setQuestion: currentQuestion', currentQuestion.id);
      console.log('debug: setQuestion: QUESTION DATA', questionData);

      console.log('debug: setQuestion: question', question.id);
    }

    this.emit(QuestionFormController.events.changeQuestion, {
      from: this.getQuestionScreenType(currentQuestion),
      to: this.getQuestionScreenType(question),
    });

    this.question(question);
    setTimeout(() => {
      if (questionData) {
        question.updateData(questionData);
      }
      setTimeout(() => {
        this.updating(false);
      });
    });
  }

  onQuestionTypeChange(type) {
    let question = this.question();

    let data = question.getCommonData();
    let screenType = this.getQuestionScreenType(question);
    question.dispose();

    const newQuestion = this.getQuestionModel({ type });

    newQuestion.updateCommonData(data, "onCreate");
    this.question(newQuestion);

    this.emit(QuestionFormController.events.changeScreenType, {
      from: screenType,
      to: this.getQuestionScreenType(newQuestion),
    });

    this.isChanged(true);

    return newQuestion;
  }

  onChange(changedQuestion) {
    if (this.updating()) return;
    if (changedQuestion !== this.question()) return;
    this.isChanged(true);
  }

  getData() {
    return this.question().getData();
  }

  updateData(questionData) {
    this.updating(true);
    this.question().updateData(questionData, "reset");
    setTimeout(() => {
      this.updating(false);
    })
  }

  setErrors(errors) {
    this.question().setErrors(errors);
  }

  isValid() {
    return this.question().isValid();
  }

  autoFill(config) {
    return this.question().autoFill(config);
  }

  onDictionaryConnectionSwitchClick(ctx) {
    if (ctx.dictionaryConnectionSwitchDisabled()) {
      ctx.info({
        text: `
          Указанный в настройках опроса справочник «${POLL.dictionary_name}» пуст. Нужно <a href="/foquz/settings?tab=settings&setting=collections" target="_blank">добавить значения в справочник</a> или выбрать другой справочник в <a href="/foquz/foquz-poll/settings?id=${POLL.id}" target="_blank">настройках опроса</a>.
        `
      });
      return false;
    }
    return true;
  }
}

QuestionFormController.events = {
  changeScreenType: "questionController.event.changeScreenType",
  changeQuestion: "questionController.event.changeQuestion",
  forcePreviewUpdate: "questionController.event.forcePreviewUpdate",
};

ko.components.register("question-form", {
  viewModel: {
    createViewModel: function (params) {
      const ViewModel = function () {
        this.types = types.typesSet;

        this.typeTemplateSelection = questionTypeOptionTemplate;

        this.type = ko.observable(null);
        this.isAuto = params.isAuto;
        this.controller = params.controller;

        this.getTemplate = (question) => {
          if (question._isSystem()) {
            if (
              question.alias() === "Товар" ||
              question.pointName() === "Товар"
            )
              return "question-form-template-items";
            else return "question-form-template-rate";
          }

          return "question-form-template-" + types.getTypeName(question.type);
        };

        this.blocked = ko.observable(false);

        this.onQuestionUpdate = () => {
          this.blocked(true);
          let question = this.controller.question();
          this.type(question.type);
          this.blocked(false);
        };

        this.controller.question.subscribe(this.onQuestionUpdate.bind(this));
        this.onQuestionUpdate();

        this.setType = (type) => {
          let newType = type;
          if (type == "start" || type == "end" || type == "five-second-test") newType = types.INTER_BLOCK;

          this.type(newType);
          this.controller.onQuestionTypeChange(newType);

          if (type == "start" || type == "end" || type == "five-second-test") {
            this.controller.question().setType(type);
            $(".block-type-select").trigger("change");
          }
        };

        this.typeSelectDisabled = ko.observable(false);
        this.aliasFieldDisabled = ko.observable(false);
        // TODO: isAuto || countAnswers() > 0
      };

      const viewModel = new ViewModel();
      return viewModel;
    },
  },
  template: {
    element: "question-form-template",
  },
});

export default QuestionFormController;
