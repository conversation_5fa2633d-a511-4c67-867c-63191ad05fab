export default function (data, mode) {
  let gallery = data.gallery.map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description,
      preview: v.poster,
      url: v.url,
      position: 'position' in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => {
    return a.position - b.position;
  });

  let detailQuestion = data.detail_question;
  let enableSelfAnswer = parseInt(data.is_self_answer) == 1;
  let enableComment =
    mode === 'cpoint'
      ? data.commentEnabled
      : data.comment_enabled || data.is_self_answer;
  let commentLengthRange =
    mode === 'cpoint'
      ? data.commentLengthRange
      : [data.comment_minlength, data.comment_maxlength];
  let placeholder = data.placeholderText;
  let variantsType = data.variants_element_type;
  let variants = (mode === 'cpoint' ? data.variants : data.detail_answers).filter((v) => !v.is_deleted).map(
    (v) => {
      return {
        id: v.id,
        value: v.variant,
      };
    },
  );

  let clarifyingQuestion = {
    enabled:
      mode === 'cpoint' ? data.clarifyingQuestionEnabled : !!detailQuestion,
    forAllRates: mode === 'cpoint' ? data.forAllRates : data.for_all_rates,
    text: (mode === 'cpoint' ? data.clarifyingQuestion : detailQuestion) || '',
    variantsType:
      mode === 'cpoint'
        ? parseInt(data.clarifyingQuestionVariantsType)
        : variantsType,
    variants:
      mode === 'cpoint'
        ? data.clarifyingQuestionAnswers.map((v) => {
            return {
              id: v.id,
              value: v.variant,
            };
          })
        : variants,
    customAnswerEnabled:
      mode === 'cpoint'
        ? data.customClarifyingQuestionAnswerAvailable
        : enableSelfAnswer,
    customAnswerLengthRange: commentLengthRange,
    customAnswerPlaceholder: placeholder,
    customAnswerLabel:  data.self_variant_text || '',
    required: data.extra_required,
  };

  let comment = {
    enabled: enableComment,
    range: commentLengthRange,
    placeholder,
    label: data.comment_label,
    required: data.comment_required,
  };

  let starsOptions = data.starRatingOptions || {};

  const aiSettingsArray = Array.isArray(data.aiSettings)
    ? data.aiSettings
    : data.aiSettings
    ? [data.aiSettings]
    : [];
  const aiSettingsEntry = aiSettingsArray.find((s) => {
    const t = 'option_type' in s ? s.option_type : s.optionType;
    return parseInt(t) === 1;
  }) || {};

  return {
    starsConfig: {
      color: starsOptions.color,
      count: starsOptions.count,
      labels: starsOptions.labelsArray,
      size: starsOptions.size,
      extraQuestionRateFrom: starsOptions.extra_question_rate_from,
      extraQuestionRateTo: starsOptions.extra_question_rate_to,
    },

    gallery,
    galleryEnabled: gallery.length > 0,

    clarifyingQuestion,
    comment,

    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || '',

    showLabels: data.show_labels ? 1 : 0,
    showNumbers: data.show_numbers  ? 1 : 0,

    ai_comment_processing: {
      sentiment: {
        enabled: ("ai_comment_sentiment_enabled" in aiSettingsEntry
          ? parseInt(aiSettingsEntry.ai_comment_sentiment_enabled) === 1
          : aiSettingsEntry.ai_tonality_enabled
          ? parseInt(aiSettingsEntry.ai_tonality_enabled) === 1
          : false),
        prompt:
          ("ai_comment_sentiment_prompt" in aiSettingsEntry
            ? aiSettingsEntry.ai_comment_sentiment_prompt
            : aiSettingsEntry.tonality_prompt) || '',
      },
      tagging: {
        enabled: ("ai_comment_tagging_enabled" in aiSettingsEntry
          ? parseInt(aiSettingsEntry.ai_comment_tagging_enabled) === 1
          : aiSettingsEntry.ai_tag_enabled
          ? parseInt(aiSettingsEntry.ai_tag_enabled) === 1
          : false),
        prompt:
          ("ai_comment_tagging_prompt" in aiSettingsEntry
            ? aiSettingsEntry.ai_comment_tagging_prompt
            : aiSettingsEntry.tag_prompt) || '',
        tags: Array.isArray(aiSettingsEntry.tags) ? aiSettingsEntry.tags : [],
      },
    },
  };
}
