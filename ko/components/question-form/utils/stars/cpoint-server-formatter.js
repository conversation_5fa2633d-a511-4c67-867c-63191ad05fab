export default function (data) {
  let question = {
    enableGallery: data.galleryEnabled ? 1 : 0,
    gallery: data.gallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    starOptions: data.starsConfig,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || '',

    show_labels: data.showLabels ? 1 : 0,
    show_numbers: data.showNumbers ? 1 : 0
  };

  let clarifyingQuestion = data.clarifyingQuestion;
  let comment = data.comment;

  if (clarifyingQuestion.enabled) {
    question.clarifyingQuestionEnabled = true;
    question.clarifyingQuestion = clarifyingQuestion.text;
    question.forAllRates = !!clarifyingQuestion.forAllRates;

    question.clarifyingQuestionVariantsType = clarifyingQuestion.variantsType;
    question.variantsType = clarifyingQuestion.variantsType;
    question.variants = clarifyingQuestion.variants.map(v => {
      return {
        id: v.id,
        variant: v.value
      }
    });
    question.customClarifyingQuestionAnswerLengthRange =
    clarifyingQuestion.customAnswerRange;
    question.placeholderText =
    clarifyingQuestion.customAnswerPlaceholder;
    question.self_variant_text = clarifyingQuestion.customAnswerLabel;

    question.customClarifyingQuestionAnswerAvailable = clarifyingQuestion.customAnswerEnabled;
    question.customVariantLengthRange = clarifyingQuestion.customAnswerRange;
    question.commentLengthRange = clarifyingQuestion.customAnswerRange;
    question.extra_required = clarifyingQuestion.required ? 1 : 0;
  }

  if (comment.enabled) {
    question.commentEnabled = true;
    question.placeholderText = comment.placeholder;
    question.commentLengthRange = comment.range;
    question.comment_label = comment.label;
    question.comment_required = data.galleryCommentRequaired;
  }

  const ai = data.ai_comment_processing || {};
  const aiCommentSentiment = ai.sentiment || {};
  const aiCommentTagging = ai.tagging || {};

  question.aiSettings = [
    {
      option_type: 1,
      ai_tonality_enabled: aiCommentSentiment.enabled ? 1 : 0,
      tonality_prompt: aiCommentSentiment.enabled ? aiCommentSentiment.prompt || '' : '',
      ai_tag_enabled: aiCommentTagging.enabled ? 1 : 0,
      tag_prompt: aiCommentTagging.enabled ? aiCommentTagging.prompt || '' : '',
    },
  ];

  return question;
}
