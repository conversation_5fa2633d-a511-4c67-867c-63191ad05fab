<template id="placeholder-template">
  <div class="form-group mb-2">
    <label class="form-label" data-bind="text: translator.t('Подсказка внутри поля')"></label>

    <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipClick, tooltipPlacement: 'top', tooltipText: customTooltip || translator.t('Можно добавить подсказку внутри поля ввода, которая будет отображаться пока ничего не введено')" type="button" title=""></button>

    <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: placeholder().length">
      <input class="form-control" data-bind="
                  textInput: placeholder, disable: $data.disabled" maxlength="125">
      <div class="chars-counter__value"></div>
    </div>

    <div class="form-hint" data-bind="text:  translator.t('Если подсказка не нужна, оставьте поле пустым')"></div>
  </div>
</template>

<template id="link-controller-block-template">
  <div class="link-field-block" data-bind="let: { $translator: $component.translator }">
    <div class="link-field-block__wrapper">

      <div>
        <switch params="checked: controller.linkWithClientField, disabled: disabled">
          <div class="d-flex align-items-center">
            <span data-bind="text: $translator.t('Связать с полем из раздела «Контакты»')"></span>
            <question-button params='text:$translator.t("Можно добавить автозаполнения контактов данными при получении ответов. Для этого нужно выбрать к какому полю из раздела \"Контакты\" добавить ответ на вопрос. Используется для быстрого получения информации по клиентам")'></question-button>
          </div>
        </switch>
      </div>

      <!-- ko template: {
        foreach: templateIf(controller.linkWithClientField(), $data),
        afterAdd: slideAfterAddFactory(400),
        beforeRemove: slideBeforeRemoveFactory(400)
      } -->

      <div class="row">
        <!-- ko if: loaded -->
        <div class="col-6">
          <div class="form-group link-field-block__field-name">
            <label class="form-label" data-bind="text: $translator.t('Связанное поле')"></label>
            <div class="select2-wrapper">
              <select data-bind="
                value: controller.linkedField,
                disable: disabled,
                valueAllowUnset: true,
                lazySelect2: {
                  templateSelection: optionTemplate,
                  templateResult: optionTemplate,
                  containerCssClass: 'form-control',
                  wrapperCssClass:
                    'select2-container--form-control link-field-block__select',
                  dropdownCssClass: 'dense-form-group__dropdown',
                  minimumResultsForSearch: 0,
                }">
                <optgroup data-bind="attr: {
                  label: $translator.t('Системные')
                }">
                  <!-- ko foreach: {data: fields.system, as: 'field'} -->
                  <option data-bind="text: field.text, value: field.id"></option>
                  <!-- /ko -->
                </optgroup>
                <optgroup data-bind="attr: {
                  label: $translator.t('Пользовательские')
                }">
                  <!-- ko foreach: {data: fields.additional, as: 'field'} -->
                  <option data-bind="text: field.text, value: field.id"></option>
                  <!-- /ko -->
                </optgroup>
              </select>
            </div>
          </div>
        </div>
        <!-- /ko -->
        <div class="col-6 ">
          <div class="form-group ghost-label">

            <foquz-checkbox params="checked: controller.rewriteExistLink, disabled: disabled">
              <div class="d-flex align-items-center">
                <span data-bind="text: $translator.t('Перезаписывать существующее значение')"></span>
              </div>
            </foquz-checkbox>


          </div>
        </div>
      </div>

      <!-- /ko -->

    </div>
  </div>
</template>


<template id="survey-question-add-video-by-link-modal-dialog-template">
  <!-- ko template: { afterRender: onInit } -->
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title" data-bind="text: _t('question', 'Добавить видео')"></h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: function() { cancel(); }">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body">
      <div class="form-group survey-question__add-video-by-link-modal-dialog-link-form-group">
        <label class="form-label" data-bind="text: _t('question', 'Ссылка на видеоролик')"></label>
        <input class="form-control" data-bind="
                               textInput: link,
                               css: {'is-invalid': formControlErrorStateMatcher(link)}
                           " placeholder="https://...">

        <!-- ko if: formControlErrorStateMatcher(link) -->
        <div class="form-error" data-bind="text: link.error()"></div>
        <!-- /ko -->
      </div>

      <div class="survey-question__add-video-by-link-modal-dialog-message">
        <img class="survey-question__add-video-by-link-modal-dialog-message-image" src="/img/youtube.png">

        <div class="survey-question__add-video-by-link-modal-dialog-message-text" data-bind="text: _t('question', 'На данный момент вы можете добавить видео только с сервиса Youtube.')">

        </div>
      </div>
    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="btn btn-link" data-bind="click: function() { cancel(); }, text: _t('Отменить')">

        </button>

        <button type="submit" class="btn btn-default" data-bind="click: function() { submit(); }, text: _t('Добавить')">

        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>

<template id="gallery-question-gallery-template">
  <div class="gallery-block">
    <div class="form-group">
      <div class="switch-form-group switch-form-group--lg mb-2">
        <label class="switch form-control">
          <input type="checkbox" data-bind="checked: question.galleryEnabled, disable: question.isFullBlocked">
          <span class="switch__slider"></span>
        </label>
        <label
          class="form-label"
          data-bind="
            text: question.translator.t('Галерея фото/видео'),
            css: {
              'form-label_checked': question.galleryEnabled,
            },
          "
        ></label>


      </div>

      <div class="form-label"><small class="form-label__note" data-bind="text: question.translator.t('Добавьте в галерею изображения или видео с вашего устройства или по ссылке')"></small></div>
    </div>



    <!-- ko template: {
      foreach: templateIf(question.galleryEnabled(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
    } -->

    <!-- ko ifnot: question.galleryController.variants().length -->
    <hr class="mb-0">
    <!-- /ko -->

    <div data-bind="dnd: function(files) {
      question.galleryController.loader.loadFiles(files);
    }, dndDisabled: question.isFullBlocked">
      <!-- ko component: {
          name: 'media-variants-controller-block',
          params: {
              controller: question.galleryController,
              blocked: question.isFullBlocked
          }
      } -->
      <!-- /ko -->

      <!-- ko ifnot: question.isFullBlocked -->
      <div class="my-4">
        <div>
          <!-- ko let: { fileInput: ko.observable(null) } -->
          <input data-bind="
          element: fileInput,
          event: {
              change: function (_, event) {
                  const file = event.target.files[0];
                  question.loadByFile(file);
                  event.target.value = '';
              }
          },
          attr: {
            accept: question.getInputAccept()
          }" type="file" hidden>

          <button class="f-btn" data-bind="click: function () { $(fileInput()).trigger('click'); }">

            <span class="f-btn-prepend">
              <svg-icon params="name: 'clip'"></svg-icon>
            </span>

            <span data-bind="text: question.translator.t('С компьютера')"></span>
          </button>

          <button class="ml-2 btn btn-default btn-with-icon btn-upload-link
          survey-question__media-form-control-actions-item" data-bind="
          click: function () { question.loadByLink(); }" type="button">
            <span data-bind="text:question.translator.t('По ссылке')"></span>
          </button>

          <!-- /ko -->
        </div>

        <!-- ko foreach: question.galleryController.loader.errors -->
        <file-loader-error params="error: $data"></file-loader-error>
        <!--/ko -->

        <!-- ko if: question.galleryError -->
        <div class="form-error" data-bind="text: question.galleryError"></div>
        <!-- /ko -->

        <!-- ko if: question.formControlErrorStateMatcher(question.galleryController.variantsCount) -->
        <div class="form-error" data-bind="text: question.galleryController.variantsCount.error()"></div>
        <!-- /ko -->
      </div>

      <dnd-cover params="type: 'mix', mode: 'multiple', dense: question.galleryController.variants().length < 1" class="my-n4 mx-n3"></dnd-cover>
      <!-- /ko -->
    </div>
    <!-- /ko -->

  </div>
</template>

<?= $this->render('./controllers/diff-controller/template.php') ?>
