@import 'Style/colors.less';

.scrollable-textarea {
  --fc-scrollable-area-scrollbar-width: 8px;
  position: relative;
  border: 1px solid #CFD8DC;
  border-radius: 4px;
  padding: 12px 6px 12px 15px;
  transition: border-color 0.2s;

  &:focus-within {
    border-color: @f-color-primary;
    box-shadow: 0 0 5px rgba(63, 101, 241, 0.5);
  }

  .copy-btn {
    z-index: 2;
    position: absolute;
    top: 13px !important;
    right: 10px !important;
    transition: opacity 250ms;
    opacity: 0.5;
    pointer-events: none;

    .fc-icon {
      color: #73808d;
      transition: all 250ms;
      cursor: pointer;
    }

    &.has-text {
      opacity: 1;
      pointer-events: auto;
    }

    &.has-text:hover .fc-icon {
      color: #2e2f31 !important;
      transform: scale(1.2);
    }
  }

  &__container {
    position: relative;

    &::before,
    &::after {
      content: "";
      position: absolute;
      left: 0;
      width: 95%;
      height: 15px;
      pointer-events: none;
      transition: opacity 0.2s;
    }

    &::before {
      top: 0;
      background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0;
    }

    &::after {
      bottom: 0;
      background: linear-gradient(0deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0;
    }

    &.shadow-top::before { opacity: 1; }
    &.shadow-bottom::after { opacity: 1; }
  }

  &__field {
    border: 0;
    padding: 0;
    padding-right: 32px;
    width: 100%;
    resize: none !important;
    line-height: 110%;
    font-size: 16px;
    min-height: 68px;

    &::-webkit-scrollbar { width: 4px; height: 4px; }
    &::-webkit-scrollbar-track { background: #E7EBED; border-radius: 4px; }
    &::-webkit-scrollbar-thumb { background: #8E99A3; border-radius: 4px; }
    &::-webkit-scrollbar-thumb:hover { background: #8E99A3; cursor: pointer; }
  }

  &--has-scrollbar {
    .copy-btn {
      right: calc(8px + var(--fc-scrollable-area-scrollbar-width)) !important;
    }

    &:not(.scrollable-textarea--has-custom-scrollbar) {
      .copy-btn {
        right: calc(16px + var(--fc-scrollable-area-scrollbar-width)) !important;
      }
      &__field {
        padding-right: 40px;
      }
    }
  }

  &--invalid {
    border-color: @f-color-danger;
  }

  &--disabled {
    background-color: #f2f5f6;
    .scrollable-textarea__field {
      opacity: 0.5;
      background: transparent !important;
    }
  }
}
