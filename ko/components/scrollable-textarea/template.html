<div class="scrollable-textarea" 
  data-bind="
    attr: { id: targetId }, 
    css: { 
      'scrollable-textarea--has-scrollbar': hasScrollbar, 
      'scrollable-textarea--has-custom-scrollbar': hasCustomScrollbar,
      'scrollable-textarea--invalid': isInvalid,
      'scrollable-textarea--disabled': disabled
    }">
  <div class="scrollable-textarea__container" data-bind="css: { 'shadow-top': hasShadowTop, 'shadow-bottom': hasShadowBottom }">
    <textarea
      class="scrollable-textarea__field"
      data-bind="
        autosizeTextarea,
        maxHeight: maxHeight,
        textInput: value,
        valueUpdate: 'input',
        disable: disabled,
        css: { 'is-invalid': isInvalid },
        attr: { readonly: readonly, maxlength: maxlength, placeholder: placeholder },
        event: { scroll: onScroll }
      "
    ></textarea>
  </div>

  <!-- ko if: (copyable && !disabled()) -->
    <button
      class="f-icon-button copy-btn"
      data-bind="
        click: copy,
        disable: disabled || !value(),
        tooltip,
        tooltipText: tooltip,
        tooltipPlacement: 'top',
        css: { 'has-text': !!value() },
        component: { name: 'foquz-icon', params: { icon: 'copy' } }
      "
    ></button>
    <fc-status-popper params="target: targetId, show: copied, mode: 'success'">
      <!-- ko text: 'Скопировано в буфер' --><!-- /ko -->
    </fc-status-popper>
  <!-- /ko -->
</div>
